#include "water_sensor.h"
#include <Arduino.h>

// Calibration values
#define WATER_SENSOR_MIN 100  // ADC value when dry
#define WATER_SENSOR_MAX 900  // ADC value when fully submerged

static uint32_t last_measurement_time = 0;
static const uint32_t MEASUREMENT_INTERVAL_MS = 1000; // 1 second

void water_sensor_init(void) {
    pinMode(WATER_SENSOR_PIN, INPUT);
}

float water_sensor_read(void) {
    int raw_value = analogRead(WATER_SENSOR_PIN);
    
    // Constrain the raw value to calibration range
    if (raw_value < WATER_SENSOR_MIN) raw_value = WATER_SENSOR_MIN;
    if (raw_value > WATER_SENSOR_MAX) raw_value = WATER_SENSOR_MAX;
    
    // Convert to percentage (0-100%)
    float percentage = (float)(raw_value - WATER_SENSOR_MIN) * 100.0f / 
                      (float)(WATER_SENSOR_MAX - WATER_SENSOR_MIN);
    
    return percentage;
}

void water_sensor_update(void) {
    uint32_t current_time = millis();
    
    // Update measurements periodically
    if (current_time - last_measurement_time >= MEASUREMENT_INTERVAL_MS) {
        float water_level = water_sensor_read();
        set_var_water_level(water_level);
        last_measurement_time = current_time;
    }
}