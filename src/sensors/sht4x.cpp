#include "sht4x.h"
#include <Arduino.h>
#include <SensirionI2CSht4x.h>
#include <Wire.h>
#include "../ui/vars.h"

// Create sensor instance
static SensirionI2CSht4x sht4x;

// Measurement interval (ms)
static uint32_t last_measurement_time = 0;
static const uint32_t MEASUREMENT_INTERVAL_MS = 2000; // 2 seconds

void sht4x_init(void) {
    // Initialize I2C
    Wire.begin();
    
    // Initialize sensor
    sht4x.begin(Wire);
    
    // Clear potential error states
    uint16_t error;
    char errorMessage[256];
    
    // Serial number identification for verification
    uint32_t serialNumber;
    error = sht4x.serialNumber(serialNumber);
    if (error) {
        Serial.println("Error trying to read serial number");
    } else {
        Serial.print("SHT4x Serial Number: ");
        Serial.println(serialNumber);
    }
}

void sht4x_update(void) {
    uint32_t current_time = millis();
    
    // Update measurements periodically
    if (current_time - last_measurement_time >= MEASUREMENT_INTERVAL_MS) {
        uint16_t error;
        char errorMessage[256];
        float temperature;
        float humidity;
        
        error = sht4x.measureHighPrecision(temperature, humidity);
        if (error) {
            Serial.print("SHT4x measurement error: ");
            errorToString(error, errorMessage, 256);
            Serial.println(errorMessage);
        } else {
            // Update UI variables
            set_var_temp(temperature);
            set_var_humidity((int32_t)humidity);
            
            // Debug output
            Serial.print("Temperature: ");
            Serial.print(temperature);
            Serial.print(" °C, Humidity: ");
            Serial.print(humidity);
            Serial.println(" %");
        }
        
        last_measurement_time = current_time;
    }
}
