#include "vars.h"

// Global variables to store sensor values
static int32_t g_humidity = 0;
static float g_temperature = 0.0f;
static int32_t g_fan_speed = 0;
static float g_water_level = 0.0f;

// Humidity variable (0-100%)
int32_t get_var_humidity() {
    return g_humidity;
}

void set_var_humidity(int32_t value) {
    if (value < 0) value = 0;
    if (value > 100) value = 100;
    g_humidity = value;
}

// Temperature variable (in Celsius)
float get_var_temp() {
    return g_temperature;
}

void set_var_temp(float value) {
    g_temperature = value;
}

// Fan speed variable (0-100%)
int32_t get_var_speed() {
    return g_fan_speed;
}

void set_var_speed(int32_t value) {
    if (value < 0) value = 0;
    if (value > 100) value = 100;
    g_fan_speed = value;
}

// Water level variable (0-100%)
float get_var_water_level() {
    return g_water_level;
}

void set_var_water_level(float value) {
    if (value < 0.0f) value = 0.0f;
    if (value > 100.0f) value = 100.0f;
    g_water_level = value;
}