#ifndef StateButton_h
#define StateButton_h

#include <Arduino.h>

enum EVENTTYPE{
    NOTHING,
    PRESS,
    HOLD
};

// TODO: Create a better system for tracking hold presses, holds and hold retriggers

// Class to track the events of the button
class PressEvent{
    EVENTTYPE type;
    size_t count;
    // size_t retriggers;
    unsigned long time;    

    public:
        PressEvent(EVENTTYPE t = NOTHING, int a = 0, unsigned long time = 0 , int retriggers = 0):
            type(t),
            count(a),
            time(time)
        {
        };

        EVENTTYPE gettype() const{
            return type;
        };

        int getcount() const{
            return count;
        };

        unsigned long eventtime() const{
            return time;
        };

        void set(EVENTTYPE t, int a){ // Set the type and count of the event
            type = t;
            count = a;
            time = millis();
        };

        void reset(){
            type = NOTHING;
            count = 0;
            // retriggers = 0;
            time = 0;
        };

        bool istype(EVENTTYPE t) const{
            return type == t;
        };

        bool isPressed(int c) const{
            return istype(PRESS) && count == c;
        };

        bool isHeld(int c) const{
            return istype(HOLD) && count == c;
        };
        
        // bool isHeldRetriggered(int c) const{
        //     return (istype(RETRIGGER) || istype(HOLD)) && count == c;
        // };
        
        bool isNothing() const{
            return istype(NOTHING);
        };
        
};


enum BUTTONSTATE{
        IDLE, // No events
        RELEASED, // Inactive state after press
        PRESSED, // Active state after press
        HELD // Held state after press
};


struct ButtonState{
    BUTTONSTATE val;
    unsigned long time;
    int count;

    void set(BUTTONSTATE v, int c){
        val = v;
        time = millis();
        count = c;
    }

    void inc(){
        count++;
    }

    void settime(){
        time = millis();
    }

    void settime(unsigned long t){
        time = t;
    }

    void reset(){
        val = IDLE;
        count = 0;
        time = 0;
    }

    BUTTONSTATE get() const{
        return val;
    }
};


class StateButton{
    
    struct RawState{
        int val;
        unsigned long time;

        void set(int s, unsigned long t){
            val = s;
            time = t;
        }
    };

    struct SteadyState{
        int val;
        unsigned long time;
        
        void set(int v, unsigned long t){
            val = v;
            time = t;
        }
    };

    RawState rawstate;
    SteadyState steadystate;
    // Config parameters
    int pin;     

    unsigned long steadyDelay;  // Jitter delay
    int activeState;            // Active state of the button, HIGH or LOW
    unsigned long pressWindow;  // Time window to check for another press
    unsigned long holdWindow;   // Time window for which the button
                                // is considered held
    int maxPresses;             // Maximum number of consecutive presses
                                // that can be registered
    unsigned long nextRetriggerTime; // Time to retrigger the hold state

    protected:
        ButtonState state;
        PressEvent pevent;

        StateButton(int pin, int maxPresses = 2, 
                    unsigned long steadyDelay = 7,  
                    int activeState = LOW, 
                    unsigned long pressWindow = 225, 
                    unsigned long holdWindow = 300)
            : state(ButtonState()),
            pevent(PressEvent()),
            pin(pin),
            steadyDelay(steadyDelay),
            activeState(activeState),
            pressWindow(pressWindow),
            holdWindow(holdWindow),
            maxPresses(maxPresses),
            nextRetriggerTime(0)
        {
            pinMode(pin, INPUT);
        };

        virtual ~StateButton();

        bool steadyRead(); // Returns true if the steady state has changed

        bool isActive() const{
            return steadystate.val == activeState;
        };

        bool isInactive() const{
            return steadystate.val != activeState;
        };

        virtual void monitorPress() = 0;

        // State transition functions
        void idle_Active();
        void released_Active();
        void released_Inactive();
        void pressed_Active();
        void pressed_Inactive();
        void held_Active();
        void held_Inactive();

    public:

        void pullUpMode() const { pinMode(pin, INPUT_PULLUP);};
   
        int getState() const{
            return state.get();
        };

        // Setters
        void setSteadyDelay(const unsigned long newdelay){
            steadyDelay = newdelay;
        };
        void setPressWindow(const unsigned long newwindow){
            pressWindow = newwindow;
        };
        void setHoldWindow(const unsigned long newwindow){
            holdWindow = newwindow;
        };
        void setMaxPresses(const int newmax){
            maxPresses = newmax;
        };
        void setActiveState(const int newstate){
            activeState = newstate;
        }

        void setNextRetriggerTime(unsigned long t){
            nextRetriggerTime = t;
        };

        void noRetrig(){
            nextRetriggerTime = 0;
        };

        // Check event
        bool noevent() const{
            return pevent.isNothing();
        }; // Check if there is no event

        bool isPressed() const{
            return pevent.istype(PRESS);
        };

        bool isHoldEvent() const{
            return pevent.istype(HOLD);
        };

        bool isPressed(int c) const{
            return pevent.isPressed(c);
        };

        bool isHoldEvent(int c) const{
            return pevent.isHeld(c);
        };

        bool isHeld(int c = 1){
            return state.get() == HELD && state.count == c;
        };

        bool isHoldEventRetrig(unsigned long t, int c = 1) const{
            if (pevent.isHeld(c)){
                auto eventtime = pevent.eventtime();
                setNextRetriggerTime(eventtime + t);
                return true;
            }
            return false;
        };

        void showEvent() const{ // Show the event type and count
                                // For debugging purposes

            if (!pevent.isNothing()){
                Serial.print("Event: ");
                Serial.println(pevent.gettype());
                Serial.print("Count: ");
                Serial.println(pevent.getcount());
            }
        };

        void debugSteadyState(){
                Serial.print("Steady State: ");
                Serial.println(steadystate.val);
                Serial.print("Time: ");
                Serial.println(steadystate.time);
        }

        void debugRawState(){
                Serial.print("Raw State: ");
                Serial.println(rawstate.val);
                Serial.print("Time: ");
                Serial.println(rawstate.time);
        }

        unsigned long getHoldDuration() const {
            if (state.get() == HELD) {
                return millis() - state.time;
            }
            return 0;
        }
};

StateButton::~StateButton() = default;

bool StateButton::steadyRead(){
    int readstate = digitalRead(pin);
    if (readstate != rawstate.val){     // Raw state changes
        rawstate.set(readstate, millis()); // Note time of change
    }
    else if (unsigned long thistime = millis(); thistime - rawstate.time > steadyDelay){ // Raw state remained the same
                                                                                        // And steady delay has passed
        if (readstate != steadystate.val){ // If the steady state is different
            steadystate.set(readstate, thistime);
            return true;
        }
    }
    return false;
};

void StateButton::idle_Active(){
    state.set(PRESSED, 1);
}

void StateButton::released_Active(){
    state.set(PRESSED, state.count + 1);
    pevent.reset();
}

void StateButton::released_Inactive(){
    if (millis() - state.time > pressWindow){
        pevent.set(PRESS, state.count);
        state.reset();
    }
}

void StateButton::pressed_Active(){
    if (millis() - state.time > holdWindow){
        state.set(HELD, state.count);
        pevent.set(HOLD, state.count);
    }
}

void StateButton::pressed_Inactive(){
    if (state.count == maxPresses){
        pevent.set(PRESS, state.count);
        state.reset();
        return;
    }
    state.set(RELEASED, state.count);
}

void StateButton::held_Active(){
    if (nextRetriggerTime == 0){
        pevent.reset();
        return;
    }
    if (millis() > nextRetriggerTime){
        pevent.set(HOLD, state.count);
        nextRetriggerTime = 0;
    }
    else{
        pevent.reset();
    }
}

void StateButton::held_Inactive(){
    state.reset();
}

class PressHoldButton: public StateButton{
    public:
        PressHoldButton(int pin, int maxPresses = 2, 
                       unsigned long steadyDelay = 7,  
                       int activeState = LOW, 
                       unsigned long pressWindow = 225, 
                       unsigned long holdWindow = 300):
            StateButton(pin, maxPresses, steadyDelay, activeState, pressWindow, holdWindow)
        {
        };

        void monitorPress() override;    
};
                                                                                                                                                                                                                                               
void PressHoldButton::monitorPress(){
    pevent.reset(); // Reset the press event
    steadyRead();

    switch (state.get()){
        case IDLE:
            if (isActive()){ // IDLE -> PRESSED
                idle_Active();
            }
            else if (isInactive()){ // IDLE -> IDLE
                pevent.reset();
            }
            break;
        case PRESSED:
            if (!isActive()){ // PRESSED -> RELEASED
                pressed_Inactive();
            }
            else if (isActive()){ // PRESSED -> HELD
                pressed_Active();
            }
            break;
        case RELEASED:
            if (isActive()){ // RELEASED -> PRESSED
                released_Active();
            }
            else if (isInactive()){ // RELEASED -> IDLE
                released_Inactive();
            }
            break;
        case HELD:
            if (!isActive()){ // HELD -> RELEASED
                held_Inactive();
            }
            else if(isActive()){ // HELD -> HELD
                held_Active();
            }
            break;
        
    }
}

#endif
